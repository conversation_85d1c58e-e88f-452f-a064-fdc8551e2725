"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const mongoose_1 = __importDefault(require("mongoose"));
const user_1 = __importDefault(require("./routes/user"));
const app = (0, express_1.default)();
//connect to database
mongoose_1.default
    .connect("mongodb://localhost/banta_for_banta")
    .then(() => {
    console.log("connected to database");
})
    .catch((err) => {
    console.log(err);
});
app.use("/", express_1.default.static("public"));
app.get("/", (req, res) => {
    res.send("welcome to banta for banta application");
});
//user routes config
app.use("/user", user_1.default);
let port = process.env.PORT || 2000;
app.listen(port, () => {
    console.log("listening on port 3000");
});
