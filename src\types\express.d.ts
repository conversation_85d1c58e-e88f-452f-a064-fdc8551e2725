import { Document } from "mongoose";

// Define the User interface based on your user schema
export interface IUser extends Document {
  _id: string;
  firstname: string;
  lastname: string;
  email: string;
  password: string;
  username: string;
  favorite_team?: string;
  favorite_player?: string;
  banta_won?: number;
  banta_lost?: number;
  level?: string;
  profile_pic?: string;
  createdAt?: Date;
  updatedAt?: Date;
}

// Extend the Express Request interface to include user property
declare global {
  namespace Express {
    interface Request {
      user?: IUser;
    }
  }
}
