import Message from "../model/messages";

export const sendMessage = async (
  senderId: string,
  receiverId: string,
  content: string,
  type: string = "text"
) => {
  try {
    const newMessage = await Message.create({
      sender: senderId,
      receiver: receiverId,
      contents: content,
      type,
    });
    return {
      status: "success",
      message: "Message sent successfully",
      data: newMessage,
    };
  } catch (err: any) {
    return {
      status: "failed",
      message: err.message,
    };
  }
};

export const getConversation = async (userId1: string, userId2: string) => {
  try {
    const messages = await Message.find({
      $or: [
        { sender: userId1, receiver: userId2 },
        { sender: userId2, receiver: userId1 },
      ],
    })
      .populate("sender", "username firstname lastname profile_pic")
      .populate("receiver", "username firstname lastname profile_pic")
      .sort({ createdAt: 1 });

    return {
      status: "success",
      data: messages,
    };
  } catch (err: any) {
    return {
      status: "failed",
      message: err.message,
    };
  }
};

export const getUserMessages = async (userId: string) => {
  try {
    const messages = await Message.find({
      $or: [{ sender: userId }, { receiver: userId }],
    })
      .populate("sender", "username firstname lastname profile_pic")
      .populate("receiver", "username firstname lastname profile_pic")
      .sort({ createdAt: -1 });

    return {
      status: "success",
      data: messages,
    };
  } catch (err: any) {
    return {
      status: "failed",
      message: err.message,
    };
  }
};

export const markMessageAsRead = async (messageId: string) => {
  try {
    const updatedMessage = await Message.findByIdAndUpdate(
      messageId,
      { isRead: true },
      { new: true }
    );

    if (!updatedMessage) {
      return {
        status: "failed",
        message: "Message not found",
      };
    }

    return {
      status: "success",
      message: "Message marked as read",
      data: updatedMessage,
    };
  } catch (err: any) {
    return {
      status: "failed",
      message: err.message,
    };
  }
};

export const deleteMessage = async (messageId: string, userId: string) => {
  try {
    const message = await Message.findById(messageId);
    
    if (!message) {
      return {
        status: "failed",
        message: "Message not found",
      };
    }

    // Only sender can delete the message
    if (message.sender.toString() !== userId) {
      return {
        status: "failed",
        message: "Unauthorized to delete this message",
      };
    }

    await Message.findByIdAndDelete(messageId);

    return {
      status: "success",
      message: "Message deleted successfully",
    };
  } catch (err: any) {
    return {
      status: "failed",
      message: err.message,
    };
  }
};
