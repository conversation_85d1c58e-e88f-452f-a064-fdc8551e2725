import mongoose, { Schema } from "mongoose";

const bantaSchema = new Schema({
  challenger: {
    type: Schema.Types.ObjectId,
    ref: "User",
    required: true,
  },
  defender: {
    type: Schema.Types.ObjectId,
    ref: "User",
    required: true,
  },
  banta_topic: {
    type: String,
    required: true,
  },
  challenger_score: {
    type: Number,
    default: 0,
  },
  defender_score: {
    type: Number,
    default: 0,
  },
  no_of_rounds: {
    type: Number,
    default: 0,
  },
  total_time: {
    type: Number,
    default: 0,
  },
  time_per_round: {
    type: Number,
    default: 0,
  },
  status: {
    type: String,
    enum: ["pending", "accepted", "rejected"],
    default: "pending",
  },
});

const banta_info = mongoose.model("banta_info", bantaSchema);

export default banta_info;
