import Post from "../model/post";

export const createPost = async (
  userId: string,
  text: string,
  image?: string
) => {
  try {
    const newPost = await Post.create({
      user: userId,
      text,
      image,
    });

    return {
      status: "success",
      message: "Post created successfully",
      data: newPost,
    };
  } catch (err: any) {
    return {
      status: "failed",
      message: err.message,
    };
  }
};

export const getAllPosts = async (page: number = 1, limit: number = 10) => {
  try {
    const skip = (page - 1) * limit;

    const posts = await Post.find()
      .populate("user", "username firstname lastname profile_pic")
      .populate("likes", "username firstname lastname")
      .populate("dislikes", "username firstname lastname")
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit);

    const totalPosts = await Post.countDocuments();

    return {
      status: "success",
      data: {
        posts,
        pagination: {
          currentPage: page,
          totalPages: Math.ceil(totalPosts / limit),
          totalPosts,
          hasNext: page < Math.ceil(totalPosts / limit),
          hasPrev: page > 1,
        },
      },
    };
  } catch (err: any) {
    return {
      status: "failed",
      message: err.message,
    };
  }
};

export const getUserPosts = async (userId: string) => {
  try {
    const posts = await Post.find({ user: userId })
      .populate("user", "username firstname lastname profile_pic")
      .populate("likes", "username firstname lastname")
      .populate("dislikes", "username firstname lastname")
      .sort({ createdAt: -1 });

    return {
      status: "success",
      data: posts,
    };
  } catch (err: any) {
    return {
      status: "failed",
      message: err.message,
    };
  }
};

export const likePost = async (postId: string, userId: string) => {
  try {
    const post = await Post.findById(postId);

    if (!post) {
      return {
        status: "failed",
        message: "Post not found",
      };
    }

    // Remove from dislikes if present
    post.dislikes = post.dislikes.filter((id) => id.toString() !== userId);

    // Toggle like
    const isLiked = post.likes.includes(userId as any);
    if (isLiked) {
      post.likes = post.likes.filter((id) => id.toString() !== userId);
    } else {
      post.likes.push(userId as any);
    }

    await post.save();

    const updatedPost = await Post.findById(postId)
      .populate("user", "username firstname lastname profile_pic")
      .populate("likes", "username firstname lastname")
      .populate("dislikes", "username firstname lastname");

    return {
      status: "success",
      message: isLiked ? "Post unliked" : "Post liked",
      data: updatedPost,
    };
  } catch (err: any) {
    return {
      status: "failed",
      message: err.message,
    };
  }
};

export const dislikePost = async (postId: string, userId: string) => {
  try {
    const post = await Post.findById(postId);

    if (!post) {
      return {
        status: "failed",
        message: "Post not found",
      };
    }

    // Remove from likes if present
    post.likes = post.likes.filter((id) => id.toString() !== userId);

    // Toggle dislike
    const isDisliked = post.dislikes.includes(userId as any);
    if (isDisliked) {
      post.dislikes = post.dislikes.filter((id) => id.toString() !== userId);
    } else {
      post.dislikes.push(userId as any);
    }

    await post.save();

    const updatedPost = await Post.findById(postId)
      .populate("user", "username firstname lastname profile_pic")
      .populate("likes", "username firstname lastname")
      .populate("dislikes", "username firstname lastname");

    return {
      status: "success",
      message: isDisliked ? "Post undisliked" : "Post disliked",
      data: updatedPost,
    };
  } catch (err: any) {
    return {
      status: "failed",
      message: err.message,
    };
  }
};

export const deletePost = async (postId: string, userId: string) => {
  try {
    const post = await Post.findById(postId);

    if (!post) {
      return {
        status: "failed",
        message: "Post not found",
      };
    }

    // Only post owner can delete
    if (post.user.toString() !== userId) {
      return {
        status: "failed",
        message: "Unauthorized to delete this post",
      };
    }

    await Post.findByIdAndDelete(postId);

    return {
      status: "success",
      message: "Post deleted successfully",
    };
  } catch (err: any) {
    return {
      status: "failed",
      message: err.message,
    };
  }
};

export const addView = async (postId: string, userId: string) => {
  try {
    const post = await Post.findById(postId);

    if (!post) {
      return {
        status: "failed",
        message: "Post not found",
      };
    }

    // Add view if not already viewed by this user
    if (!post.views.includes(userId as any)) {
      post.views.push(userId as any);
      await post.save();
    }

    return {
      status: "success",
      message: "View added",
    };
  } catch (err: any) {
    return {
      status: "failed",
      message: err.message,
    };
  }
};
