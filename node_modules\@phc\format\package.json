{"name": "@phc/format", "version": "1.0.0", "description": "PHC string format serializer/deserializer", "license": "MIT", "homepage": "https://github.com/simonepri/phc-format#readme", "repository": "github:simonepri/phc-format", "bugs": {"url": "https://github.com/simonepri/phc-format/issues", "email": "<EMAIL>"}, "author": "<PERSON> <<EMAIL>> (https://simoneprimarosa.com)", "contributors": ["<PERSON> <<EMAIL>> (https://simoneprimarosa.com)"], "keywords": ["mcf", "phc", "modular", "crypt", "passwords", "hashing", "competition", "password", "standard", "crypto"], "main": "index.js", "files": ["index.js"], "engines": {"node": ">=10"}, "scripts": {"lint": "xo", "test": "nyc ava", "release": "npx np", "update": "npx npm-check -u"}, "dependencies": {}, "devDependencies": {"ava": "^3.9.0", "nyc": "^15.1.0", "xo": "~0.27.2"}, "ava": {"verbose": true}, "nyc": {"reporter": ["lcovonly", "text"]}, "xo": {"prettier": true, "space": true}}