import { Request, Response, NextFunction } from "express";
import jwt from "jsonwebtoken";
import { findUserById } from "../service/user";
import { IUser } from "../types";

export const verifyUser = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    // Extract token from authorization header
    const { authorization = "" } = req.headers;
    let jwt_secret = process.env.JWT_SECRET as string;
    const decoded: any = jwt.verify(authorization, jwt_secret);

    const foundId = decoded.user;

    // Find client by decoded user ID
    const foundUser: any = await findUserById(foundId);
    if (foundUser.status == "failed") {
      return res.status(401).json({
        status: "failed",
        message: "Unauthorized",
      });
    }
    if (foundUser) {
      // attach user to request object - now properly typed
      req.user = foundUser as IUser;
      return next();
    } else {
      return res.status(401).json({
        status: "failed",
        message: "Unauthorized",
      });
    }
  } catch (err: any) {
    return res.status(401).json({
      status: "failed",
      message: "Unauthorized:" + err.message,
    });
  }
};
