"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.create_user = void 0;
const user_1 = require("../service/user");
const create_user = async (req, res) => {
    try {
        const { firstname, lastname, email, username, password } = req.body;
        const createdUser = await (0, user_1.createUser)(firstname, lastname, email, username, password);
        return res.status(200).json({
            status: "success",
            message: "User created successfully",
            data: createdUser,
        });
    }
    catch (err) {
        return res.status(500).json({
            status: "error",
            message: err.message,
        });
    }
};
exports.create_user = create_user;
