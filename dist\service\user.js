"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.createUser = void 0;
const users_1 = __importDefault(require("../model/users"));
const createUser = async (firstname, lastname, email, password, username, favorite_player, favorite_team) => {
    try {
        const newUser = await users_1.default.create({
            firstname,
            lastname,
            email,
            username,
            password,
        });
        if (newUser) {
            return {
                status: "success",
                message: "user account created successfuly",
                data: newUser,
            };
        }
        else {
            return {
                status: "failed",
                message: "something went wrong",
            };
        }
    }
    catch (err) {
        return {
            status: "failed",
            message: err.message,
        };
    }
};
exports.createUser = createUser;
