import User from "../model/users";
import argon from "argon2";

export const createUser = async (
  firstname: string,
  lastname: string,
  email: string,
  username: string,
  password: string,
  favorite_player?: string,
  favorite_team?: string
) => {
  try {
    let hashpassword = await argon.hash(password);
    const newUser = await User.create({
      firstname,
      lastname,
      email,
      username,
      password: hashpassword,
    });
    return newUser;
  } catch (err: any) {
    return {
      status: "failed",
      message: err.message,
    };
  }
};

export const getAllUser = async () => {
  try {
    const allUser = await User.find();
    return allUser;
  } catch (err: any) {
    return {
      status: "failed",
      message: err.message,
    };
  }
};

export const findUserByEmail = async (email: string) => {
  try {
    const foundUser = await User.findOne({ email });
    if (foundUser) {
      return foundUser;
    } else {
      return {
        status: "failed",
        message: "no user found with this email",
      };
    }
  } catch (err: any) {
    return {
      status: "failed",
      message: err.message,
    };
  }
};

export const findUserByUsername = async (username: string) => {
  try {
    const foundUser = await User.findOne({ username });
    if (foundUser) {
      return foundUser;
    } else {
      return {
        status: "failed",
        message: "no user found with this username",
      };
    }
  } catch (err: any) {
    return {
      status: "failed",
      message: err.message,
    };
  }
};

export const findUserById = async (id: string) => {
  try {
    const foundUser = await User.findById(id);
    if (foundUser) {
      return foundUser;
    } else {
      return {
        status: "failed",
        message: "no user found with this id",
      };
    }
  } catch (err: any) {
    return {
      status: "failed",
      message: err.message,
    };
  }
};
